# ChargingStats

**Version:** 1.2.0

A React Native app for monitoring charging statistics.

## Features
- Real-time battery statistics including:
  - Voltage monitoring
  - Current monitoring
  - Temperature monitoring (with warning indicators for high temperatures)
  - Battery health status
  - Charging state
- Charging power display
- Runtime graphs
- AdMob banner ads (test ads in development mode)

## Current Measurement
The app automatically handles different device current reporting units:
- Values > 10000: Treated as microamperes (µA)
- Values ≤ 10000: Treated as milliamperes (mA)

This adaptive approach ensures accurate current readings across different Android devices without requiring device-specific configurations.

## AdMob Setup
1. Create an AdMob account at https://admob.google.com/
2. Create a new app in AdMob for both Android and iOS
3. Get your app IDs from AdMob dashboard
4. Update app.json with your real app IDs:
```json
"react-native-google-mobile-ads": {
  "android_app_id": "YOUR_ANDROID_APP_ID",
  "ios_app_id": "YOUR_IOS_APP_ID"
}
```
5. Test ads are enabled by default. Replace with real ad unit IDs before production.

## Setting App Icon

To set a custom app icon:

1. Visit https://appicon.co/
2. Upload your AppIcon.jpg
3. Download the generated icons
4. Replace the following files:

### Android
- Replace files in `android/app/src/main/res/mipmap-*` directories:
  - mdpi: 48x48
  - hdpi: 72x72
  - xhdpi: 96x96
  - xxhdpi: 144x144
  - xxxhdpi: 192x192

### iOS
- Replace files in `ios/ChargingStats/Images.xcassets/AppIcon.appiconset`:
  - 20x20, 29x29, 40x40, 58x58, 60x60, 76x76, 80x80, 87x87, 120x120, 152x152, 167x167, 180x180

5. Rebuild the app:
```bash
npx react-native run-android
npx react-native run-ios
```

## Development
To start the development server:
```bash
npx react-native start
```

To run on Android:
```bash
npx react-native run-android
```

To run on iOS:
```bash
npx react-native run-ios
