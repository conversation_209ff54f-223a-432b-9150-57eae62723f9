package com.chargingstats

import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.content.Context
import android.os.Build
import android.os.Build.VERSION_CODES
import androidx.annotation.RequiresApi
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.Arguments
import com.facebook.react.modules.core.DeviceEventManagerModule
import android.content.BroadcastReceiver
import android.util.Log
import com.facebook.react.bridge.ReactContext
import com.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter

class BatteryModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private var batteryChangeReceiver: BroadcastReceiver? = null

    override fun getName(): String {
        return "BatteryModule"
    }

    @ReactMethod
    fun getBatteryStats(promise: Promise) {
        try {
            val stats = getCurrentBatteryStats()
            promise.resolve(stats)
        } catch (e: Exception) {
            promise.reject("BATTERY_ERROR", e)
        }
    }

    @ReactMethod
    fun addBatteryChangeListener() {
        if (batteryChangeReceiver == null) {
            batteryChangeReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val stats = getCurrentBatteryStats()
                    sendEvent("batteryChange", stats)
                }
            }

            val ifilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
            reactApplicationContext.registerReceiver(batteryChangeReceiver, ifilter)
        }
    }

    @ReactMethod
    fun removeBatteryChangeListener() {
        try {
            batteryChangeReceiver?.let {
                reactApplicationContext.unregisterReceiver(it)
                batteryChangeReceiver = null
            }
        } catch (e: Exception) {
            Log.e("BatteryModule", "Error removing battery change listener: ${e.message}")
        }
    }

    @ReactMethod
    fun removeBatteryChangeListenerWithPromise(promise: Promise) {
        try {
            batteryChangeReceiver?.let {
                reactApplicationContext.unregisterReceiver(it)
                batteryChangeReceiver = null
            }
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("LISTENER_ERROR", e)
        }
    }
    private fun getBatteryPercentage(context: Context): Int {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
        } else {
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            val level = batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            val scale = batteryIntent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
            if (scale > 0) (level * 100f / scale).toInt() else -1
        }
    }

    private fun getCurrentBatteryStats(): WritableMap {
        val ifilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        val batteryStatus = reactApplicationContext.registerReceiver(null, ifilter)
        val batteryManager = reactApplicationContext.getSystemService(Context.BATTERY_SERVICE) as BatteryManager

        // Basic battery info
        val voltage = batteryStatus?.getIntExtra(BatteryManager.EXTRA_VOLTAGE, -1) ?: -1
        val currentNow = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)

        val statusInt = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS)
        val isCharging = statusInt == BatteryManager.BATTERY_STATUS_CHARGING
        val health = batteryStatus?.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_UNKNOWN) ?: BatteryManager.BATTERY_HEALTH_UNKNOWN
        val temperature = batteryStatus?.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1) ?: -1
        val deviceModel = Build.MODEL

        // New battery information
        // Cycle count is only available on API 28 (Android P) and above
        val cycleCount = try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                batteryStatus?.getIntExtra(BatteryManager.EXTRA_CYCLE_COUNT, -1) ?: -1
            } else {
                -1
            }
        } catch (e: Exception) {
            -1
        }

        val batteryCapacity = try {
            batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_COUNTER) / 1000 // Convert to mAh
        } catch (e: Exception) {
            -1
        }

        val batteryLevel = batteryStatus?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = batteryStatus?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
        val batteryPercentage = getBatteryPercentage(reactApplicationContext)

        // Charging technology
        val chargingType = when (batteryStatus?.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1)) {
            BatteryManager.BATTERY_PLUGGED_AC -> "AC"
            BatteryManager.BATTERY_PLUGGED_USB -> "USB"
            BatteryManager.BATTERY_PLUGGED_WIRELESS -> "Wireless"
            else -> "On Battery"
        }

        val batteryTechnology = batteryStatus?.getStringExtra(BatteryManager.EXTRA_TECHNOLOGY) ?: "Unknown"

        // Design capacity vs current capacity
        val designCapacity = try {
            batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CHARGE_COUNTER) / 1000 // mAh
        } catch (e: Exception) {
            -1
        }
        val currentCapacity = if (designCapacity > 0 && batteryPercentage > 0) {
            (designCapacity * batteryPercentage / 100f).toInt()
        } else -1
        var amps = if (Math.abs(currentNow) > 10000) {
            currentNow / 1000.0  // Convert microA to mA
        } else {
            currentNow/1.0     // keep mA
        }
        //when isCharge is true, the value of currentNow should be positive and vice versa
        if (isCharging && currentNow < 0) {
            amps *= -1
        } else if (!isCharging && currentNow > 0) {
            amps *= -1
        }
        // Estimated time remaining calculation
        val timeRemaining = if (isCharging) {
            if (amps > 0) {
                val remainingCapacity = (designCapacity - currentCapacity).toDouble()
                (remainingCapacity / (amps) * 3600).toInt() // Time in seconds
            } else -1
        } else {
            if (amps < 0) {
                val remainingCapacity = currentCapacity.toDouble()
                (remainingCapacity / (Math.abs(amps)) * 3600).toInt() // Time in seconds
            } else -1
        }

        // Convert voltage from millivolts to volts
        val volts = voltage / 1000.0

        // Convert current to amps
        // If value > 10000, assume it's in microamperes (µA), otherwise assume milliamperes (mA)


        // Convert temperature from tenths of a degree Celsius to Celsius
        val tempCelsius = temperature / 10.0

        return createBatteryStats(
            volts, amps, isCharging, health, deviceModel, tempCelsius,
            cycleCount, batteryCapacity, batteryPercentage, chargingType,
            timeRemaining, batteryTechnology, currentCapacity, designCapacity
        )
    }

    private fun createBatteryStats(
        voltage: Double, current: Double, isCharging: Boolean, health: Int,
        deviceModel: String, temperature: Double, cycleCount: Int,
        batteryCapacity: Int, batteryLevel: Int, chargingType: String,
        timeRemaining: Int, batteryTechnology: String,
        currentCapacity: Int, designCapacity: Int
    ): WritableMap {
        val map = Arguments.createMap()
        map.putDouble("voltage", voltage)
        map.putDouble("current", current)
        map.putBoolean("isCharging", isCharging)
        map.putInt("health", health)
        map.putString("deviceModel", deviceModel)
        map.putDouble("temperature", temperature)
        map.putInt("cycleCount", cycleCount)
        map.putInt("batteryCapacity", batteryCapacity)
        map.putInt("batteryLevel", batteryLevel)
        map.putString("chargingType", chargingType)
        map.putInt("timeRemaining", timeRemaining)
        map.putString("batteryTechnology", batteryTechnology)
        map.putInt("currentCapacity", currentCapacity)
        map.putInt("designCapacity", designCapacity)
        return map
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        try {
            val eventEmitter = reactApplicationContext
                .getJSModule(RCTDeviceEventEmitter::class.java)
            eventEmitter?.emit(eventName, params)
        } catch (e: Exception) {
            Log.e("BatteryModule", "Error sending event: ${e.message}")
        }
    }

    // Required for NativeEventEmitter in React Native
    @ReactMethod
    fun addListener(eventName: String) {
        // Keep track of listeners registered for the event
        // This is required for RN 0.65+
    }

    @ReactMethod
    fun removeListeners(count: Int) {
        // Remove listeners
        // This is required for RN 0.65+
    }
}
