import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import { Dimensions } from 'react-native';
import { NativeModules } from 'react-native';
import { theme, commonStyles } from '../theme';
import { AD_IDS } from '../utils/Constants';
import { AdComponent } from './AdComponent';
import { TopBannerAd } from './TopBannerAd';
import { useAdStore } from '../store/adStore';

const { BatteryModule } = NativeModules;
const screenWidth = Dimensions.get('window').width;

export default function RuntimeGraphs() {
  const [voltageData, setVoltageData] = useState(Array(20).fill(0));
  const [currentData, setCurrentData] = useState(Array(20).fill(0));
  const [powerData, setPowerData] = useState(Array(20).fill(0));
  const [temperatureData, setTemperatureData] = useState(Array(20).fill(0));
  const [activeTab, setActiveTab] = useState('voltage');
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Fade in animation when component mounts
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    const interval = setInterval(() => {
      BatteryModule.getBatteryStats()
        .then((stats: any) => {
          const voltage = stats.voltage;
          const current = stats.current / 1000;
          const power = voltage * current;

          setVoltageData(prev => [...prev.slice(-19), voltage]);
          setCurrentData(prev => [...prev.slice(-19), current]);
          setPowerData(prev => [...prev.slice(-19), power]);
          setTemperatureData(prev => [...prev.slice(-19), stats.temperature]);
        })
        .catch((error: any) => {
          console.error('Failed to get battery stats:', error);
        });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Get chart configuration based on data type
  const getChartConfig = (dataType: string) => {
    let gradientFrom, gradientTo, lineColor, dotColor;
    
    switch(dataType) {
      case 'voltage':
        gradientFrom = theme.colors.primary;
        gradientTo = theme.colors.secondary;
        lineColor = 'rgba(67, 97, 238, 1)';
        dotColor = theme.colors.secondary;
        break;
      case 'current':
        gradientFrom = theme.colors.secondary;
        gradientTo = theme.colors.highlight;
        lineColor = 'rgba(60, 207, 207, 1)';
        dotColor = theme.colors.highlight;
        break;
      case 'power':
        gradientFrom = theme.colors.highlight;
        gradientTo = theme.colors.primary;
        lineColor = 'rgba(114, 9, 183, 1)';
        dotColor = theme.colors.primary;
        break;
      case 'temperature':
        gradientFrom = '#F44336';
        gradientTo = '#FF9800';
        lineColor = 'rgba(244, 67, 54, 1)';
        dotColor = '#FF9800';
        break;
      default:
        gradientFrom = theme.colors.primary;
        gradientTo = theme.colors.secondary;
        lineColor = 'rgba(67, 97, 238, 1)';
        dotColor = theme.colors.secondary;
    }
    
    return {
      backgroundGradientFrom: theme.colors.cardBackground,
      backgroundGradientTo: theme.colors.cardBackground,
      backgroundGradientFromOpacity: 1,
      backgroundGradientToOpacity: 1,
      decimalPlaces: 2,
      color: (opacity = 1) => lineColor,
      labelColor: () => theme.colors.textSecondary,
      strokeWidth: 3,
      barPercentage: 0.5,
      useShadowColorFromDataset: false,
      fillShadowGradient: gradientFrom,
      fillShadowGradientOpacity: 0.3,
      propsForBackgroundLines: {
        strokeDasharray: '5, 5',
        strokeWidth: 1,
        stroke: theme.colors.border,
      },
      propsForDots: {
        r: '4',
        strokeWidth: '2',
        stroke: dotColor
      },
    };
  };

  // Get current value for the active chart
  const getCurrentValue = () => {
    switch(activeTab) {
      case 'voltage':
        return voltageData[voltageData.length - 1]?.toFixed(2) + ' V';
      case 'current':
        return currentData[currentData.length - 1]?.toFixed(2) + ' A';
      case 'power':
        return powerData[powerData.length - 1]?.toFixed(2) + ' W';
      case 'temperature':
        return temperatureData[temperatureData.length - 1]?.toFixed(1) + ' °C';
      default:
        return '0';
    }
  };

  // Get data for the active chart
  const getActiveData = () => {
    switch(activeTab) {
      case 'voltage': return voltageData;
      case 'current': return currentData;
      case 'power': return powerData;
      case 'temperature': return temperatureData;
      default: return voltageData;
    }
  };

  // Get unit for the active chart
  const getActiveUnit = () => {
    switch(activeTab) {
      case 'voltage': return 'V';
      case 'current': return 'A';
      case 'power': return 'W';
      case 'temperature': return '°C';
      default: return '';
    }
  };

  // Handle tab change with animation
  const handleTabChange = (tab: string) => {
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();
    
    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      <TopBannerAd bannerId={AD_IDS.BANNER.TOP_RUNTIME_GRAPHS} />
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <Text style={commonStyles.screenTitle}>Runtime Graphs</Text>
      
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'voltage' && styles.activeTab]}
          onPress={() => handleTabChange('voltage')}
        >
          <Text style={[styles.tabText, activeTab === 'voltage' && styles.activeTabText]}>Voltage</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'current' && styles.activeTab]}
          onPress={() => handleTabChange('current')}
        >
          <Text style={[styles.tabText, activeTab === 'current' && styles.activeTabText]}>Current</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'power' && styles.activeTab]}
          onPress={() => handleTabChange('power')}
        >
          <Text style={[styles.tabText, activeTab === 'power' && styles.activeTabText]}>Power</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'temperature' && styles.activeTab]}
          onPress={() => handleTabChange('temperature')}
        >
          <Text style={[styles.tabText, activeTab === 'temperature' && styles.activeTabText]}>Temp</Text>
        </TouchableOpacity>
      </View>
      
      <TopBannerAd bannerId={AD_IDS.BANNER.GRAPH_PAGE_2} />
      {/* Current Value Display */}
      <View style={styles.currentValueContainer}>
        <Text style={styles.currentValueLabel}>Current {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}</Text>
        <Text style={styles.currentValue}>{getCurrentValue()}</Text>
      </View>
      
      {/* Chart */}
      <Animated.View style={[styles.chartContainer, { opacity: fadeAnim }]}>
        <LineChart
          data={{
            labels: ['', '', '', '', '', ''],
            datasets: [{
              data: getActiveData(),
              color: (opacity = 1) => getChartConfig(activeTab).color(opacity),
              strokeWidth: 3
            }]
          }}
          width={screenWidth - 40}
          height={280}
          yAxisSuffix={getActiveUnit()}
          yAxisInterval={1}
          chartConfig={getChartConfig(activeTab)}
          bezier
          style={styles.chart}
          withInnerLines={true}
          withOuterLines={false}
          withVerticalLines={false}
          withHorizontalLines={true}
          withVerticalLabels={false}
          withHorizontalLabels={true}
          fromZero={activeTab !== 'voltage'}
          segments={5}
        />
      </Animated.View>
      
      {/* Stats Cards */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statsScrollView}>
        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Min</Text>
          <Text style={styles.statsValue}>
            {Math.min(...getActiveData().filter(val => val > 0)).toFixed(2)} {getActiveUnit()}
          </Text>
        </View>
        
        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Max</Text>
          <Text style={styles.statsValue}>
            {Math.max(...getActiveData()).toFixed(2)} {getActiveUnit()}
          </Text>
        </View>
        
        <View style={styles.statsCard}>
          <Text style={styles.statsLabel}>Avg</Text>
          <Text style={styles.statsValue}>
            {(getActiveData().reduce((a, b) => a + b, 0) / getActiveData().filter(val => val > 0).length).toFixed(2)} {getActiveUnit()}
          </Text>
        </View>
      </ScrollView>
      
      <AdComponent
        nativeId={AD_IDS.NATIVE.GRAPH_PAGE_1}
        bannerId={AD_IDS.BANNER.GRAPH_PAGE_1}
        showAdMedia={true}
      />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    padding: theme.spacing.medium,
    paddingBottom: theme.spacing.xlarge,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.pill,
    marginVertical: theme.spacing.medium,
    padding: theme.spacing.xsmall,
    ...theme.shadow.small,
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.small,
    alignItems: 'center',
    borderRadius: theme.borderRadius.pill,
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
    ...theme.shadow.small,
  },
  tabText: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabText: {
    color: theme.colors.text,
    fontWeight: '600',
  },
  currentValueContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.medium,
  },
  currentValueLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xsmall,
  },
  currentValue: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
  },
  chartContainer: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    marginBottom: theme.spacing.medium,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  chart: {
    borderRadius: theme.borderRadius.medium,
    paddingRight: theme.spacing.small,
  },
  statsScrollView: {
    marginBottom: theme.spacing.medium,
  },
  statsCard: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    marginRight: theme.spacing.medium,
    width: 100,
    alignItems: 'center',
    ...theme.shadow.small,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statsLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.small,
  },
  statsValue: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.text,
  },
});
