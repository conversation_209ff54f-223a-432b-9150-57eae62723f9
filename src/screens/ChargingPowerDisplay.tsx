import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Animated } from 'react-native';
import { getBatteryStats } from '../utils/powerUtils';
import { theme, commonStyles } from '../theme';
import { AD_IDS } from '../utils/Constants';
import { AdComponent } from './AdComponent';
import { TopBannerAd } from './TopBannerAd';
import { useAdStore } from '../store/adStore';

interface ChargingPowerDisplayProps {
  navigation: {
    navigate: (screen: string) => void;
  };
}

export default function ChargingPowerDisplay({ navigation }: ChargingPowerDisplayProps) {
  const [voltage, setVoltage] = useState(0);
  const [current, setCurrent] = useState(0);
  const [power, setPower] = useState(0);
  const [maxPower, setMaxPower] = useState(25);
  const [powerAnimation] = useState(new Animated.Value(0));
  const { isAdFree } = useAdStore();

  useEffect(() => {
    const fetchBatteryStats = async () => {
      const stats = await getBatteryStats();

      const newVoltage = parseFloat(stats.voltage.toFixed(2));
      const newCurrent = parseFloat(stats.current.toFixed(2));
      // Calculate power and ensure it has exactly 2 decimal places
      const calculatedPower = newVoltage * (newCurrent / 1000);
      const newPower = parseFloat(stats.power.toFixed(2));
      
      setVoltage(newVoltage);
      setCurrent(newCurrent);
      setPower(newPower);
      
      // Update maxPower if current power is higher
      if (newPower > maxPower) {
        setMaxPower(newPower);
      }
      
      // Animate power value change
      Animated.spring(powerAnimation, {
        toValue: newPower,
        friction: 7,
        tension: 40,
        useNativeDriver: false,
      }).start();
    };

    fetchBatteryStats();
    const interval = setInterval(fetchBatteryStats, 1000);
    return () => clearInterval(interval);
  }, []);

  // Calculate power percentage for gauge with dynamic maxPower
  const powerPercentage = Math.min(power / maxPower, 1) * 100;
  
  // Determine power color based on value
  const getPowerColor = () => {
    if (power < 5) return theme.colors.textSecondary;
    if (power < 10) return theme.colors.secondary;
    if (power < 15) return theme.colors.primary;
    return theme.colors.highlight;
  };

  return (
    <View style={styles.container}>
      <TopBannerAd bannerId={AD_IDS.BANNER.TOP_CHARGING_POWER} />
      <ScrollView
        contentContainerStyle={[
          styles.scrollContent,
          { justifyContent: isAdFree ? 'center' : 'space-between' }
        ]}
      >
        <Text style={commonStyles.screenTitle}>Battery Charger</Text>
        
        {/* Power Gauge */}
        <View style={styles.gaugeContainer}>
          <View style={styles.gauge}>
            <View
              style={[
                styles.gaugeLevel,
                { width: `${powerPercentage}%`, backgroundColor: getPowerColor() }
              ]}
            />
          </View>
          <View style={styles.powerValueContainer}>
            <Animated.Text style={[styles.powerValue, { color: getPowerColor() }]}>
              {power.toFixed(2)}
            </Animated.Text>
            <Text style={styles.powerUnit}>W</Text>
          </View>
        </View>
        <TopBannerAd bannerId={AD_IDS.BANNER.CHARGING_POWER} />
        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Voltage</Text>
            <Text style={styles.statValue}>{voltage}</Text>
            <Text style={styles.statUnit}>V</Text>
          </View>
          
          <View style={styles.statCard}>
            <Text style={styles.statLabel}>Current</Text>
            <Text style={styles.statValue}>{current}</Text>
            <Text style={styles.statUnit}>mA</Text>
          </View>
        </View>
        
        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={commonStyles.button}
            onPress={() => navigation.navigate('BatteryStats')}
          >
            <Text style={commonStyles.buttonText}>Battery Statistics</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={commonStyles.buttonSecondary}
            onPress={() => navigation.navigate('RuntimeGraphs')}
          >
            <Text style={commonStyles.buttonTextSecondary}>View Graphs</Text>
          </TouchableOpacity>
        </View>
        
        <AdComponent
          nativeId={AD_IDS.NATIVE.CHARGING_POWER}
          bannerId={AD_IDS.BANNER.CHARGING_POWER}
          showAdMedia={true}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    alignItems: 'center',
    padding: theme.spacing.large,
    paddingBottom: theme.spacing.xlarge,
  },
  gaugeContainer: {
    width: '100%',
    alignItems: 'center',
    marginVertical: theme.spacing.xlarge,
  },
  gauge: {
    width: '100%',
    height: 12,
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.pill,
    overflow: 'hidden',
    marginBottom: theme.spacing.medium,
  },
  gaugeLevel: {
    height: '100%',
    borderRadius: theme.borderRadius.pill,
  },
  powerValueContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  powerValue: {
    fontSize: theme.typography.value.fontSize,
    fontWeight: theme.typography.value.fontWeight as '700',
  },
  powerUnit: {
    fontSize: theme.typography.subheading.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    marginLeft: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginVertical: theme.spacing.large,
  },
  statCard: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.medium,
    width: '48%',
    alignItems: 'center',
    ...theme.shadow.small,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statLabel: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.small,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
  },
  statUnit: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  buttonContainer: {
    width: '100%',
    marginVertical: theme.spacing.large,
  }
});
