import React from "react";
import { View, StyleSheet } from "react-native";
import { BannerAd, BannerAdSize } from "react-native-google-mobile-ads";
import { useAdStore } from "../store/adStore";
import { theme } from "../theme";

interface TopBannerAdProps {
  bannerId: string;
}

export const TopBannerAd: React.FC<TopBannerAdProps> = ({ bannerId }) => {
  const { isAdFree } = useAdStore();

  if (isAdFree) {
    return null;
  }

  return (
    <View style={styles.container}>
      <BannerAd
        unitId={bannerId}
        size={BannerAdSize.BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: theme.colors.cardBackground,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    paddingVertical: theme.spacing.xsmall,
    marginBottom: theme.spacing.small,
    ...theme.shadow.small,
  },
});