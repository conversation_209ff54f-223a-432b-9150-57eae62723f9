import { StyleSheet } from 'react-native';

export const theme = {
  colors: {
    primary: '#4361EE', // Modern blue
    secondary: '#3CCFCF', // Teal accent
    background: '#121212', // Dark background for better contrast
    cardBackground: '#1E1E1E', // Slightly lighter than background for cards
    text: '#FFFFFF',
    textSecondary: '#B0B0B0', // Softer secondary text
    border: '#2C2C2C', // Subtle border
    success: '#4CAF50', // More muted green
    warning: '#FF9800', // Orange for warnings
    error: '#F44336', // Red for errors
    highlight: '#7209B7', // Purple highlight for important elements
    gradient: {
      start: '#4361EE',
      end: '#3CCFCF',
    },
  },
  spacing: {
    xsmall: 4,
    small: 8,
    medium: 16,
    large: 24,
    xlarge: 32,
    xxlarge: 48,
  },
  typography: {
    heading: {
      fontSize: 28,
      fontWeight: '700',
      letterSpacing: 0.25,
    },
    subheading: {
      fontSize: 22,
      fontWeight: '600',
      letterSpacing: 0.15,
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
      letterSpacing: 0.5,
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      letterSpacing: 0.4,
      color: '#B0B0B0',
    },
    value: {
      fontSize: 42,
      fontWeight: '700',
      letterSpacing: -0.5,
    },
  },
  borderRadius: {
    small: 8,
    medium: 12,
    large: 16,
    pill: 50,
  },
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 4,
    },
  },
};

export const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    padding: theme.spacing.medium,
    backgroundColor: theme.colors.background,
  },
  card: {
    backgroundColor: theme.colors.cardBackground,
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.large,
    marginBottom: theme.spacing.medium,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.small,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    marginBottom: theme.spacing.small,
  },
  button: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.medium,
    paddingHorizontal: theme.spacing.large,
    borderRadius: theme.borderRadius.pill,
    alignItems: 'center',
    marginVertical: theme.spacing.medium,
    ...theme.shadow.small,
  },
  buttonSecondary: {
    backgroundColor: 'transparent',
    paddingVertical: theme.spacing.medium,
    paddingHorizontal: theme.spacing.large,
    borderRadius: theme.borderRadius.pill,
    alignItems: 'center',
    marginVertical: theme.spacing.medium,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  buttonText: {
    color: theme.colors.text,
    fontSize: theme.typography.body.fontSize,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  buttonTextSecondary: {
    color: theme.colors.primary,
    fontSize: theme.typography.body.fontSize,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  label: {
    fontSize: theme.typography.caption.fontSize,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xsmall,
    letterSpacing: theme.typography.caption.letterSpacing,
  },
  value: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.text,
    marginBottom: theme.spacing.small,
    fontWeight: '500',
  },
  valueHighlight: {
    fontSize: theme.typography.subheading.fontSize,
    color: theme.colors.primary,
    fontWeight: '700',
    marginBottom: theme.spacing.small,
  },
  sectionTitle: {
    fontSize: theme.typography.subheading.fontSize,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.medium,
    marginTop: theme.spacing.medium,
  },
  screenTitle: {
    fontSize: theme.typography.heading.fontSize,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: theme.spacing.large,
    textAlign: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20', // 20% opacity
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.medium,
  },
});
