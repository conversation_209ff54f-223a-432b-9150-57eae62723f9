import React, { useState, useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  Pressable,
  Text,
  Modal,
  View,
  StyleSheet,
  Linking,
  Alert,
  TouchableOpacity,
  Animated
} from 'react-native';
import { iapService } from './services/iapService';
import { useAdStore } from './store/adStore';
import ChargingPowerDisplay from './screens/ChargingPowerDisplay';
import BatteryStats from './screens/BatteryStats';
import RuntimeGraphs from './screens/RuntimeGraphs';
import { theme } from './theme';

const Stack = createNativeStackNavigator<RootStackParamList>();

export type RootStackParamList = {
  ChargingPowerDisplay: undefined;
  BatteryStats: undefined;
  RuntimeGraphs: undefined;
};

export default function Navigation() {
  const [menuVisible, setMenuVisible] = useState(false);

  const handleRateApp = () => {
    const storeUrl = 'market://details?id=com.chargingstats';
    Linking.openURL(storeUrl).catch(() => {
      Linking.openURL('https://play.google.com/store/apps/details?id=com.chargingstats');
    });
    setMenuVisible(false);
  };

  const { isAdFree, setAdFree, initializeAdState } = useAdStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await initializeAdState();
        await iapService.init();
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };
    
    initializeApp();
    
    return () => {
      try {
        iapService.cleanup();
      } catch (error) {
        console.error('Error cleaning up IAP service:', error);
      }
    };
  }, []);

  const handleRemoveAds = async () => {
    try {
      if (isAdFree) {
        Alert.alert('Already Ad-Free', 'You have already removed ads from the app.');
        return;
      }

      // First try to get the product info
      try {
        await iapService.getRemoveAdsProduct();
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Unable to get product information. Please try again later.');
        return;
      }

      // If product info is successful, try to make the purchase
      try {
        await iapService.purchaseRemoveAds();
        // Note: setAdFree is now handled in the purchaseUpdatedListener when purchase is confirmed
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Failed to complete purchase. Please try again later.');
      }
    } catch (error) {
      console.error('Purchase flow error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again later.');
    } finally {
      setMenuVisible(false);
    }
  };

  // Animation for menu
  const [menuAnimation] = useState(new Animated.Value(0));
  
  useEffect(() => {
    Animated.timing(menuAnimation, {
      toValue: menuVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [menuVisible]);

  // Custom header button component
  const MenuButton = () => (
    <TouchableOpacity
      onPress={() => setMenuVisible(true)}
      style={styles.menuButton}
      activeOpacity={0.7}
    >
      <View style={styles.menuDot} />
      <View style={styles.menuDot} />
      <View style={styles.menuDot} />
    </TouchableOpacity>
  );

  return (
    <>
      <Modal
        animationType="none"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setMenuVisible(false)}
        >
          <Animated.View
            style={[
              styles.menuContainer,
              {
                opacity: menuAnimation,
                transform: [
                  {
                    translateY: menuAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleRateApp}
              activeOpacity={0.7}
            >
              <View style={styles.menuIconContainer}>
                <Text style={styles.menuIcon}>⭐</Text>
              </View>
              <Text style={styles.menuText}>Rate App</Text>
            </TouchableOpacity>
            
            <View style={styles.menuDivider} />
            
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleRemoveAds}
              activeOpacity={0.7}
            >
              <View style={styles.menuIconContainer}>
                <Text style={styles.menuIcon}>🚫</Text>
              </View>
              <Text style={styles.menuText}>
                {isAdFree ? 'Ads Removed ✓' : 'Remove Ads'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </Pressable>
      </Modal>

      <Stack.Navigator
        initialRouteName="ChargingPowerDisplay"
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTitleStyle: {
            color: theme.colors.text,
            fontSize: 18,
            fontWeight: '600',
          },
          headerTintColor: theme.colors.primary,
          headerRight: () => <MenuButton />,
          headerShadowVisible: false,
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen
          name="ChargingPowerDisplay"
          component={ChargingPowerDisplay}
          options={{
            title: 'Battery Charger',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="BatteryStats"
          component={BatteryStats}
          options={{
            title: 'Battery Statistics',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="RuntimeGraphs"
          component={RuntimeGraphs}
          options={{
            title: 'Runtime Graphs',
            headerTitleAlign: 'center',
          }}
        />
      </Stack.Navigator>
    </>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
  },
  menuContainer: {
    backgroundColor: theme.colors.cardBackground,
    marginTop: 60,
    marginRight: 16,
    marginLeft: 'auto',
    borderRadius: theme.borderRadius.medium,
    padding: theme.spacing.small,
    width: 220,
    ...theme.shadow.medium,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.medium,
    borderRadius: theme.borderRadius.small,
  },
  menuDivider: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: theme.spacing.xsmall,
  },
  menuText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  menuIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary + '20', // 20% opacity
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.medium,
  },
  menuIcon: {
    fontSize: 16,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '10', // 10% opacity
    marginRight: theme.spacing.medium,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.primary,
    marginVertical: 2,
  },
});
