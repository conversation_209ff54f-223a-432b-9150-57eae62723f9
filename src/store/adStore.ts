import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';

interface AdState {
  isAdFree: boolean;
  setAdFree: (value: boolean) => void;
  initializeAdState: () => Promise<void>;
}

export const useAdStore = create<AdState>((set) => ({
  isAdFree: false,
  setAdFree: (value: boolean) => {
    set({ isAdFree: value });
    AsyncStorage.setItem('isAdFree', JSON.stringify(value));
  },
  initializeAdState: async () => {
    try {
      const value = await AsyncStorage.getItem('isAdFree');
      if (value !== null) {
        set({ isAdFree: JSON.parse(value) });
      }
    } catch (error) {
      set({ isAdFree: false });
      console.error('Error loading ad-free state:', error);
    }
  },
}));
