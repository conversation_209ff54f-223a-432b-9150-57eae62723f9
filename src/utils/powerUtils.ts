
import { NativeModules } from 'react-native';
export const calculatePower = (voltage: number, current: number): number => {
  return voltage * current/1000;
};

export interface BatteryStats {
  voltage: number;
  current: number;
  power: number;
}

export const getBatteryStats = async (): Promise<BatteryStats> => {
  const { BatteryModule } = NativeModules;
  
  const stats = await BatteryModule.getBatteryStats();
  const power = calculatePower(stats.voltage, stats.current);

  return {
    voltage: stats.voltage,
    current: stats.current,
    power
  };
};
