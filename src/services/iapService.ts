import { Alert, Platform } from 'react-native';
import {
  initConnection,
  purchaseErrorListener,
  purchaseUpdatedListener,
  ProductPurchase,
  finishTransaction,
  requestPurchase,
  getProducts,
  getAvailablePurchases,
} from 'react-native-iap';
import { useAdStore } from '../store/adStore'; // Importing the ad store

const REMOVE_ADS_SKU = Platform.select({
  ios: 'com.chargingstats.removeads',
  android: 'com.chargingstats.removeads',
});

class IAPService {
  private purchaseUpdateSubscription: any;
  private purchaseErrorSubscription: any;
  private lastErrorTimestamp: number = 0;
  private readonly ERROR_DEBOUNCE_MS = 1000; // 1 second debounce

  async init() {
    try {
      await initConnection();
      await this.checkIfAdFree(); // Check if the user already owns the remove ads feature
      this.setupListeners();
    } catch (error: unknown) {
      const timestamp = new Date().toISOString();
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error(`Error initializing IAP at ${timestamp}:`, error);
    }
  }

  private async checkIfAdFree() {
    try {
      const purchases = await getAvailablePurchases();
      const hasPurchasedRemoveAds = purchases.some(purchase =>
        purchase.productId === REMOVE_ADS_SKU
      );
      
      if (hasPurchasedRemoveAds) {
        try {
        const { setAdFree } = useAdStore.getState();
        setAdFree(true); // Set ad-free state if the user already owns the item
        } catch (storeError) {
          console.error('Error updating ad store state:', storeError);
        }
      }
    } catch (error) {
      console.error('Error checking available purchases:', error);
      // Don't throw the error to prevent app crashes
    }
  }

  private setupListeners() {
    try {
      this.purchaseUpdateSubscription = purchaseUpdatedListener(
        async (purchase: ProductPurchase) => {
          if (!purchase) {
            console.log('Received empty purchase update');
            return;
          }
          
          try {
            // First check if this is the remove ads product
            if (purchase.productId === REMOVE_ADS_SKU) {
              try {
              const { setAdFree } = useAdStore.getState(); // Accessing the ad store
              setAdFree(true); // Setting the ad-free state to true
              } catch (storeError) {
                console.error('Error updating ad store state:', storeError);
            }
            }
            
            // Then finish the transaction
            await finishTransaction({
              purchase,
              isConsumable: false,
            });
          } catch (error: any) {
            const timestamp = new Date().toISOString();
            const errorMessage = error.message || 'Unknown error occurred';
            const errorDetails = {
              code: error.code,
              debugMessage: error.debugMessage,
              responseCode: error.responseCode
            };
            console.error(`Error finishing transaction at ${timestamp}:`, {
              message: errorMessage,
              ...errorDetails
            });
          }
        }
      );
    } catch (error) {
      console.error('Error setting up purchase updated listener:', error);
    }

    try {
      this.purchaseErrorSubscription = purchaseErrorListener((error: any) => {
        const currentTime = Date.now();
        // Debounce errors that occur too quickly
        if (currentTime - this.lastErrorTimestamp < this.ERROR_DEBOUNCE_MS) {
          return;
        }
        this.lastErrorTimestamp = currentTime;
        
        const timestamp = new Date().toISOString();
        const errorMessage = error?.message || 'Unknown error occurred';
        const errorDetails = {
          code: error?.code,
          debugMessage: error?.debugMessage,
          responseCode: error?.responseCode
        };
        
        console.error(`Purchase error at ${timestamp}:`, {
          message: errorMessage,
          ...errorDetails
        });
      });
    } catch (error) {
      console.error('Error setting up purchase error listener:', error);
    }
  }

  async getRemoveAdsProduct() {
    try {
      const products = await getProducts({
        skus: [REMOVE_ADS_SKU!]
      });
      return products[0];
    } catch (error: any) {
      const timestamp = new Date().toISOString();
      const errorMessage = error.message || 'Unknown error occurred';
      const errorDetails = {
        code: error.code,
        debugMessage: error.debugMessage,
        responseCode: error.responseCode
      };
      console.error(`Error getting products at ${timestamp}:`, {
        message: errorMessage,
        ...errorDetails
      });
      return null;
    }
  }

  async purchaseRemoveAds() {
    try {
      if (!REMOVE_ADS_SKU) {
        throw new Error('SKU not defined for this platform');
      }
      await requestPurchase({
        skus:  [REMOVE_ADS_SKU!],
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
      return true;
    } catch (error: any) {
      const timestamp = new Date().toISOString();
      const errorMessage = error.message || 'Unknown error occurred';
      const errorDetails = {
        code: error.code,
        debugMessage: error.debugMessage,
        responseCode: error.responseCode
      };
      Alert.alert('Purchase error', errorMessage);
      console.error(`Error purchasing remove ads at ${timestamp}:`, {
        message: errorMessage,
        ...errorDetails
      });
      return false;
    }
  }

  cleanup() {
    try {
      if (this.purchaseUpdateSubscription) {
        this.purchaseUpdateSubscription.remove();
        this.purchaseUpdateSubscription = null;
      }
    } catch (error) {
      console.error('Error removing purchase update subscription:', error);
    }
    
    try {
      if (this.purchaseErrorSubscription) {
        this.purchaseErrorSubscription.remove();
        this.purchaseErrorSubscription = null;
      }
    } catch (error) {
      console.error('Error removing purchase error subscription:', error);
    }
  }
}

export const iapService = new IAPService();
