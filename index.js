import { AppRegistry } from 'react-native';
import App from './src/App';
import { name as appName } from './app.json';
import mobileAds from 'react-native-google-mobile-ads';

// Wrap in try-catch for better error handling
try {
  mobileAds()
    .initialize()
    .then(adapterStatuses => {
      console.log('Mobile Ads initialization complete:', adapterStatuses);
    })
    .catch(error => {
      console.error('Mobile Ads initialization failed:', error);
    });
} catch (error) {
  console.error('Error during Mobile Ads setup:', error);
}

AppRegistry.registerComponent(appName, () => App);
