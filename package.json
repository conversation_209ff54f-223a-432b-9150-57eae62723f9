{"name": "ChargingStats", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "android-release": "npx react-native build-android --mode=release", "android-clean": "cd android && rm -rf app/src/main/res/drawable-mdpi&& ./gradlew clean  && cd ../", "react-bundle": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "release": "npm run android-clean  && npm run android-release", "test-release": "react-native run-android --mode=release", "brt": "npm i && npm run release && npm run test-release", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "react": "18.3.1", "react-native": "0.76.5", "react-native-chart-kit": "^6.12.0", "react-native-google-mobile-ads": "^14.11.0", "react-native-iap": "^12.16.1", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-svg": "^15.10.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/react": "^18.3.18", "@types/react-native": "^0.72.8", "@types/react-redux": "^7.1.34", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}